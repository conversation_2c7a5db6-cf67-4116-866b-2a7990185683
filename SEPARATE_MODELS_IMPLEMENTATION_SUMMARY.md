# Separate Models Implementation - Complete Guide

## 🎯 Overview

I have successfully created separate models for Move In and Move Out data, replacing the generic `LikeAndDisLikeUsers` model with specialized models that better represent the different data structures and use cases.

## 🔧 What Was Implemented

### 1. **New Model Files Created**

#### **MoveOutModel** (`lib/models/move_out_model/move_out_model.dart`)
- **`MoveOutModel`**: Main response model for move out API
- **`MoveOutUser`**: Specialized user model for move out data
- **Enhanced Properties**:
  - `profilePicture`: User profile image
  - `department`: User department
  - `contactNumber`: Contact information
  - `about`: User description
  - `createdAt`/`updatedAt`: Timestamps
  - `isLiked`/`isDisliked`/`isFavorite`: Interaction states
- **Helper Methods**:
  - `displayName`: Safe name display
  - `profileImageUrl`: Profile picture URL handling
  - `isActive`: Active status check

#### **MoveInModel** (`lib/models/move_in_model/move_in_model.dart`)
- **`MoveInModel`**: Main response model for move in API
- **`MoveInUser`**: Specialized user model for move in data
- **Enhanced Properties**:
  - `profilePicture`: User profile image
  - `department`: User department
  - `contactNumber`: Contact information
  - `about`: User description
  - `createdAt`/`updatedAt`: Timestamps
  - `isAccepted`/`isPending`/`isRejected`: Connection states
  - `connectionStatus`: Connection status string
  - `likedAt`: When the user was liked
  - `mutualConnections`: Mutual connection info
- **Helper Methods**:
  - `displayName`: Safe name display
  - `profileImageUrl`: Profile picture URL handling
  - `isActive`: Active status check
  - `connectionStatusDisplay`: Human-readable status
  - `canAccept`: Whether connection can be accepted

### 2. **Updated Repository** (`lib/repository/like_repository/like_repository.dart`)
- **`getMoveOutData()`**: Returns `MoveOutModel` instead of `LikeModel`
- **`getMoveInData()`**: Returns `MoveInModel` instead of `LikeModel`
- Maintained backward compatibility for other methods

### 3. **Updated BLoC Layer**

#### **LikeState** (`lib/viewmodels/like_bloc/like_state.dart`)
- **`moveOutData`**: Changed from `List<LikeAndDisLikeUsers>` to `List<MoveOutUser>`
- **`moveInData`**: Changed from `List<LikeAndDisLikeUsers>` to `List<MoveInUser>`
- Updated `copyWith` method to use new types

#### **LikeBloc** (`lib/viewmodels/like_bloc/like_bloc.dart`)
- **`_onLoadMoveOutData`**: Uses `MoveOutModel` and `MoveOutUser`
- **`_onLoadMoveInData`**: Uses `MoveInModel` and `MoveInUser`
- Proper type safety throughout

### 4. **Updated UI Layer** (`lib/views/like_view/like_screen.dart`)

#### **Enhanced User Cards**
- **`_buildMoveOutUserCard`**: Now accepts `MoveOutUser`
  - Shows department information if available
  - Uses `displayName` helper method
  - Enhanced user information display

- **`_buildMoveInUserCard`**: Now accepts `MoveInUser`
  - Shows department information if available
  - Uses `connectionStatusDisplay` for status
  - Color-coded status indicators:
    - 🟢 Green: Accepted
    - 🟠 Orange: Pending
    - 🔴 Red: Rejected
    - ⚫ Grey: Unknown

#### **Automatic Data Loading**
- Converted to `StatefulWidget` for proper initialization
- Automatic API calls on screen load
- Smart tab switching with conditional loading

## 📊 Key Improvements

### ✅ **Type Safety**
- Eliminated generic `LikeAndDisLikeUsers` usage
- Specific models for specific use cases
- Compile-time type checking

### ✅ **Enhanced Data Structure**
- **Move Out Users**: Focus on discovery and interaction
  - Like/dislike/favorite states
  - Profile and contact information
- **Move In Users**: Focus on connection management
  - Accept/pending/reject states
  - Connection timestamps and mutual info

### ✅ **Better User Experience**
- More relevant information displayed per context
- Appropriate action buttons per user type
- Status indicators that make sense for each context

### ✅ **Maintainability**
- Clear separation of concerns
- Self-documenting code with helper methods
- Easy to extend with new properties

## 🔌 API Integration

### Move Out API (`/profile-like-outwards/`)
```dart
MoveOutModel moveOut = await likeRepository.getMoveOutData();
List<MoveOutUser> users = moveOut.data ?? [];
```

### Move In API (`/profile-like-inwards/`)
```dart
MoveInModel moveIn = await likeRepository.getMoveInData();
List<MoveInUser> users = moveIn.data ?? [];
```

## 🎮 Usage Examples

### Display Move Out User
```dart
MoveOutUser user = state.moveOutData[index];
Text(user.displayName)  // Safe name display
Text('Department: ${user.department}')  // Department info
bool canLike = !user.isLiked  // Check if can like
```

### Display Move In User
```dart
MoveInUser user = state.moveInData[index];
Text(user.displayName)  // Safe name display
Text(user.connectionStatusDisplay)  // "Accepted", "Pending", etc.
bool canAccept = user.canAccept  // Check if can accept
```

## 🚀 Benefits

1. **Clearer Code**: Each model represents exactly what it's used for
2. **Better Validation**: Type system catches misuse at compile time
3. **Enhanced Features**: Each model has context-specific properties
4. **Future-Proof**: Easy to add new properties without affecting other models
5. **Better Testing**: Specific models make unit testing more focused

## 📝 Migration Notes

- **Old**: `LikeAndDisLikeUsers` was used for both move in and move out
- **New**: `MoveOutUser` for move out, `MoveInUser` for move in
- **Backward Compatibility**: Original `LikeModel` still exists for other uses
- **No Breaking Changes**: Existing functionality preserved

## 🔧 Next Steps

1. **Test the Implementation**: Run the app and verify both tabs load correctly
2. **Add Action Handlers**: Implement like/dislike for move out, accept/reject for move in
3. **Profile Pictures**: Add image loading for profile pictures
4. **Error Handling**: Add specific error messages for each model type
5. **Caching**: Consider adding local caching for better performance

The implementation now provides a solid foundation for handling different types of user interactions with appropriate data models and UI components.
