class MoveInModel {
  bool? status;
  String? message;
  List<MoveInUser>? data;

  MoveInModel({this.status, this.message, this.data});

  factory MoveInModel.fromJson(Map<String, dynamic> json) {
    return MoveInModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null
          ? List<MoveInUser>.from(
              json['data'].map((item) => MoveInUser.fromJson(item)),
            )
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.map((item) => item.toJson()).toList(),
    };
  }
}

class MoveInUser {
  int? id;
  String? name;
  String? year;
  bool? status;
  String? profileImage;
  String? about;

  MoveInUser({
    this.id,
    this.name,
    this.year,
    this.status,
    this.profileImage,
    this.about,
  });

  factory MoveInUser.fromJson(Map<String, dynamic> json) {
    return MoveInUser(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      year: json['year'] ?? '',
      status: json['status'] ?? false,
      profileImage: json['profile_image'] ?? '',
      about: json['about'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'year': year,
      'status': status,
      'profile_image': profileImage,
      'about': about,
    };
  }

}
