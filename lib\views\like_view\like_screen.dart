import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/models/like_model/like_model.dart';
import 'package:room_eight/viewmodels/like_bloc/like_bloc.dart';

class LikeScreen extends StatefulWidget {
  const LikeScreen({super.key});
  static Widget builder(BuildContext context) => const LikeScreen();

  @override
  State<LikeScreen> createState() => _LikeScreenState();
}

class _LikeScreenState extends State<LikeScreen> {
  @override
  void initState() {
    super.initState();
    // Load data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<LikeBloc>().add(LoadMoveOutData());
      context.read<LikeBloc>().add(LoadMoveInData());
    });
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: Theme.of(context).customColors.scaffoldColor,
        floatingActionButton: GestureDetector(
          onTap: () => NavigatorService.pushNamed(AppRoutes.chatscreen),
          child: Padding(
            padding: const EdgeInsets.only(bottom: 70.0),
            child: Container(
              margin: const EdgeInsets.only(right: 16.0),
              height: 56.h,
              width: 56.w,
              decoration: BoxDecoration(
                color: Theme.of(context).customColors.primaryColor,
                borderRadius: BorderRadius.circular(16.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.shade300,
                    blurRadius: 5.r,
                    offset: Offset(1, 2),
                  ),
                ],
              ),
              child: Center(child: Icon(Icons.add)),
            ),
          ),
        ),
        body: BlocBuilder<LikeBloc, LikeState>(
          builder: (context, state) {
            if (state.isLoadData) {
              return CircularProgressIndicator();
            }
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.r),
              child: Column(
                children: [
                  buildSizedBoxH(50.h),
                  _buildTabView(context),
                  // Add refresh buttons for testing individual events
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.w,
                      vertical: 8.h,
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              context.read<LikeBloc>().add(LoadMoveOutData());
                            },
                            child: const Text('Refresh Move Out'),
                          ),
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              context.read<LikeBloc>().add(LoadMoveInData());
                            },
                            child: const Text('Refresh Move In'),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: PageView(
                      controller: context.read<LikeBloc>().pageController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        _buildMoveOutView(context),
                        _buildMoveInView(context),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildTabView(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h),
      decoration: BoxDecoration(
        color: Theme.of(
          context,
        ).customColors.fillColor, // <-- your desired background color
        borderRadius: BorderRadius.circular(
          100.r,
        ), // optional, if you want rounded background
      ),
      child: TabBar(
        onTap: (index) {
          context.read<LikeBloc>().add(TabChangedEvent(index));
          // Load data for the selected tab if not already loaded
          if (index == 0) {
            // Move Out tab
            final state = context.read<LikeBloc>().state;
            if (state.moveOutData.isEmpty && !state.isLoadingMoveOut) {
              context.read<LikeBloc>().add(LoadMoveOutData());
            }
          } else if (index == 1) {
            // Move In tab
            final state = context.read<LikeBloc>().state;
            if (state.moveInData.isEmpty && !state.isLoadingMoveIn) {
              context.read<LikeBloc>().add(LoadMoveInData());
            }
          }
        },
        indicatorSize: TabBarIndicatorSize.tab,
        labelStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
          fontSize: 16.sp,
          fontWeight: FontWeight.w500,
        ),
        labelColor: Theme.of(context).customColors.fillColor,
        dividerColor: Colors.transparent,
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        labelPadding: EdgeInsets.all(0.r),
        indicator: ShapeDecoration(
          color: Theme.of(context).customColors.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(100.r),
          ),
        ),
        tabs: [
          Tab(text: Lang.of(context).lbl_move_out),
          Tab(text: Lang.of(context).lbl_move_in),
        ],
      ),
    );
  }

  Widget _buildMoveOutView(BuildContext context) {
    return BlocBuilder<LikeBloc, LikeState>(
      builder: (context, state) {
        // Show loading indicator if data is being loaded
        if (state.isLoadingMoveOut) {
          return const Center(child: CircularProgressIndicator());
        }

        // Show empty state if no data
        if (state.moveOutData.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.favorite_border, size: 64.r, color: Colors.grey),
                buildSizedBoxH(16.h),
                Text(
                  'No Move Out data available',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey,
                    fontSize: 16.sp,
                  ),
                ),
                buildSizedBoxH(20.h),
                ElevatedButton(
                  onPressed: () {
                    context.read<LikeBloc>().add(LoadMoveOutData());
                  },
                  child: const Text('Refresh'),
                ),
              ],
            ),
          );
        }

        // Show data list
        return RefreshIndicator(
          onRefresh: () async {
            context.read<LikeBloc>().add(LoadMoveOutData());
          },
          child: ListView.builder(
            padding: EdgeInsets.symmetric(vertical: 20.h),
            itemCount: state.moveOutData.length,
            itemBuilder: (context, index) {
              final user = state.moveOutData[index];
              return Padding(
                padding: EdgeInsets.only(bottom: 20.h),
                child: _buildMoveOutUserCard(
                  context: context,
                  user: user,
                  index: index,
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildMoveInView(BuildContext context) {
    return BlocBuilder<LikeBloc, LikeState>(
      builder: (context, state) {
        // Show loading indicator if data is being loaded
        if (state.isLoadingMoveIn) {
          return const Center(child: CircularProgressIndicator());
        }

        // Show empty state if no data
        if (state.moveInData.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.favorite, size: 64.r, color: Colors.grey),
                buildSizedBoxH(16.h),
                Text(
                  'No Move In data available',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey,
                    fontSize: 16.sp,
                  ),
                ),
                buildSizedBoxH(20.h),
                ElevatedButton(
                  onPressed: () {
                    context.read<LikeBloc>().add(LoadMoveInData());
                  },
                  child: const Text('Refresh'),
                ),
              ],
            ),
          );
        }

        // Show data list
        return RefreshIndicator(
          onRefresh: () async {
            context.read<LikeBloc>().add(LoadMoveInData());
          },
          child: ListView.builder(
            padding: EdgeInsets.symmetric(vertical: 20.h),
            itemCount: state.moveInData.length,
            itemBuilder: (context, index) {
              final user = state.moveInData[index];
              return Padding(
                padding: EdgeInsets.only(bottom: 20.h),
                child: _buildMoveInUserCard(
                  context: context,
                  user: user,
                  index: index,
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildMoveOutUserCard({
    required BuildContext context,
    required LikeAndDisLikeUsers user,
    required int index,
  }) {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 30.r,
                backgroundColor: Theme.of(context).primaryColor,
                child: Text(
                  user.name?.isNotEmpty == true
                      ? user.name![0].toUpperCase()
                      : 'U',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.name ?? 'Unknown User',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: 16.sp,
                      ),
                    ),
                    buildSizedBoxH(4.h),
                    Text(
                      'Year: ${user.year ?? 'N/A'}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey,
                        fontSize: 14.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          buildSizedBoxH(16.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildActionButton(
                context: context,
                icon: Icons.close,
                color: Colors.red,
                onTap: () {
                  // Handle dislike action
                },
              ),
              _buildActionButton(
                context: context,
                icon: Icons.favorite,
                color: Colors.pink,
                onTap: () {
                  // Handle favorite action
                },
              ),
              _buildActionButton(
                context: context,
                icon: Icons.favorite_border,
                color: Colors.green,
                onTap: () {
                  // Handle like action
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMoveInUserCard({
    required BuildContext context,
    required LikeAndDisLikeUsers user,
    required int index,
  }) {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 30.r,
                backgroundColor: Theme.of(context).primaryColor,
                child: Text(
                  user.name?.isNotEmpty == true
                      ? user.name![0].toUpperCase()
                      : 'U',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.name ?? 'Unknown User',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: 16.sp,
                      ),
                    ),
                    buildSizedBoxH(4.h),
                    Text(
                      'Year: ${user.year ?? 'N/A'}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey,
                        fontSize: 14.sp,
                      ),
                    ),
                    buildSizedBoxH(4.h),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 4.h,
                      ),
                      decoration: BoxDecoration(
                        color: user.status == true
                            ? Colors.green
                            : Colors.orange,
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Text(
                        user.status == true ? 'Active' : 'Pending',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          buildSizedBoxH(16.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildActionButton(
                context: context,
                icon: Icons.message,
                color: Colors.blue,
                onTap: () {
                  // Handle message action
                },
              ),
              _buildActionButton(
                context: context,
                icon: Icons.person_add,
                color: Colors.green,
                onTap: () {
                  // Handle connect action
                },
              ),
              _buildActionButton(
                context: context,
                icon: Icons.info,
                color: Colors.grey,
                onTap: () {
                  // Handle info action
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required BuildContext context,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(12.r),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Icon(icon, color: color, size: 20.r),
      ),
    );
  }

  Widget buildUserProfileCard({
    required BuildContext context,
    required String imageUrl,
    required String name,
    required String department,
    required VoidCallback onLike,
    required VoidCallback onDislike,
    required VoidCallback onFavorite,
  }) {
    return Container(
      padding: EdgeInsets.all(8.r),
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.fillColor,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(color: Colors.black12, blurRadius: 8, offset: Offset(0, 4)),
        ],
      ),
      child: Column(
        children: [
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(12.r),
                child: Image.network(
                  imageUrl,
                  height: 180.h,
                  width: double.infinity,
                  fit: BoxFit.cover,
                ),
              ),
              Positioned(
                top: 10.h,
                right: 10.w,
                child: InkWell(
                  onTap: onFavorite,
                  child: CircleAvatar(
                    radius: 20.r,
                    backgroundColor: Colors.white70,
                    child: CustomImageView(
                      imagePath: Assets.images.svgs.icons.icLike.path,
                      margin: EdgeInsets.all(10.r),
                    ),
                  ),
                ),
              ),
            ],
          ),
          buildSizedBoxH(12.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                name,
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16.sp),
              ),
              Row(
                children: [
                  Container(
                    height: 30.h,
                    width: 30.w,
                    decoration: BoxDecoration(
                      color: Theme.of(context).customColors.fillColor,
                      borderRadius: BorderRadius.circular(100.r),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.shade300,
                          blurRadius: 5.r,
                          offset: Offset(1, 2),
                        ),
                      ],
                    ),
                    child: CustomImageView(
                      imagePath: Assets.images.svgs.icons.icClose.path,
                      margin: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 8.h,
                      ),
                    ),
                  ),
                  buildSizedboxW(5.w),
                  Container(
                    height: 30.h,
                    width: 30.w,
                    decoration: BoxDecoration(
                      color: Theme.of(context).customColors.fillColor,
                      borderRadius: BorderRadius.circular(100.r),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.shade300,
                          blurRadius: 5.r,
                          offset: Offset(1, 2),
                        ),
                      ],
                    ),
                    child: CustomImageView(
                      imagePath: Assets.images.svgs.icons.icWirite.path,
                      margin: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 8.h,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          Row(
            children: [
              Text(
                department,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontSize: 14.sp,
                  color: Theme.of(context).customColors.darkGreytextcolor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
